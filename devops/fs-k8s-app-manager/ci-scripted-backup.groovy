//class Module {
//  String gitUrl
//  String module
//  String tag
//  String artifactBaseImage
//  String artifactImage
//  String commitId
//  boolean exist
//}

/**
 * 自带的echo无法直接展现输出内容
 * 参考：https://stackoverflow.com/questions/49564140/how-can-i-force-jenkins-blue-ocean-to-display-print-output-instead-of-print-mes
 */
def echoWithLabel(String message, String label) {
  if (label) {
    sh(label: label, script: "echo -e \"$message\"")
  } else {
    echo message
  }
}

static def jarVersionNecessaryConf(String parentPom) {
  def ret = [
    'notifier-support'                  : '4.2.0-SNAPSHOT',
    'fs-paas-auth-api'                  : '1.0.0-SNAPSHOT',
    'fs-paas-auth-client'               : '2.0.0-SNAPSHOT',
    'biz-log-client'                    : '1.1.0-SNAPSHOT',
    'biz-log-proto'                     : '1.1.0-SNAPSHOT',
    'rpc-trace'                         : '4.0.0-SNAPSHOT',
    'spring-support'                    : '3.0.0-SNAPSHOT',
    'mongo-spring-support'              : '3.0.0-SNAPSHOT',
    'mybatis-spring-support'            : '4.4.0-SNAPSHOT',
    'jdbc-support'                      : '4.1.1-SNAPSHOT',
    'jedis-spring-support'              : '4.2.0-SNAPSHOT',
    'config-core'                       : '7.5.0-SNAPSHOT',
    'dubbo'                             : '2.9.4-SNAPSHOT',
    'zkclient'                          : '0.11',
    'core-filter'                       : '3.2.0-SNAPSHOT',
    'dubbo-support'                     : '1.1.2',
    'metrics-oss'                       : '6.0.0-SNAPSHOT',
    'fastjson'                          : '1.2.83',
    'jedis'                             : '3.7.1',
    'commons-pool2'                     : '2.10.0',
    'logconfig-core'                    : '3.0.0-SNAPSHOT',
    'jutil'                             : '1.1.0-SNAPSHOT',
    'netty-all'                         : '4.1.74.Final',
    'netty-common'                      : '4.1.74.Final',
    'netty-buffer'                      : '4.1.74.Final',
    'netty-codec'                       : '4.1.74.Final',
    'netty-transport'                   : '4.1.74.Final',
    'i18n-client'                       : '3.2.8-SNAPSHOT',
    'http-spring-support'               : '3.4.0-SNAPSHOT',
    'caffeine'                          : '2.9.3',
    'ehcache'                           : '3.8.1',
    'fs-paas-license-api'               : '1.1.0-SNAPSHOT',
    'validation-api'                    : '2.0.0.Final',
    'fs-restful-common'                 : '1.0.2-SNAPSHOT',
    'fs-restful-server'                 : '1.0.2-SNAPSHOT',
    'fs-restful-client'                 : '1.0.2-SNAPSHOT',
    'fs-resteasy-support'               : '1.0.2-SNAPSHOT',
    'fs-metadata-provider'              : '7.7.0-SNAPSHOT',
    'fs-common-gray-release'            : '2.2.0-SNAPSHOT',
    'gray-release'                      : '1.0.4',
    'postgresql'                        : '42.3.5',
    'fs-fsi-proxy'                      : '3.0.0-SNAPSHOT',
    'fs-uc-api'                         : '1.0.3-SNAPSHOT',
    'fs-enterprise-id-account-converter': '1.1-SNAPSHOT',
    'xstream'                           : '1.4.20',
  ]
  if (parentPom.endsWith("-alpha")) {
    ret['fs-paas-auth-api'] = '2.4.0-SNAPSHOT'
    ret['fs-paas-auth-client'] = '2.4.0-SNAPSHOT'
    ret['okhttp'] = '3.14.9'
    ret['fs-social-api'] = '1.0.2-SNAPSHOT'
//    ret['fs-stone-commons-client'] = '1.6.0-SNAPSHOT'
    ret['rocketmq-client'] = '5.3.0'
    ret['fs-rocketmq-support'] = '5.3.0-SNAPSHOT'
    ret['fs-common-mq'] = '5.3.0-SNAPSHOT'
    ret['fs-uc-api'] = '1.1.2-SNAPSHOT'
    ret['fs-sandbox-api'] = '2.0.4-SNAPSHOT'
    ret['fs-cache'] = '2.0.1-SNAPSHOT'
    ret['fs-change-set-api'] = '2.0.4-SNAPSHOT'
  } else if (parentPom.endsWith("-forceecrm-rc")) {
    ret['mq-dispatcher'] = '5.0.2-SNAPSHOT'
    ret['rocketmq-client'] = '5.3.0'
    ret['fs-common-mq'] = '5.3.0-SNAPSHOT'
    ret['fs-rocketmq-support'] = '5.3.0-SNAPSHOT'
    ret['fs-uc-api'] = '1.1.2-SNAPSHOT'
    ret['fs-sandbox-api'] = '2.0.4-SNAPSHOT'
    ret['fs-paas-app-flow'] = '9.3.0-SNAPSHOT'
    ret['fs-metadata-provider'] = '9.3.0-SNAPSHOT'
    ret['fs-cache'] = '2.0.1-SNAPSHOT'
    ret['fs-change-set-api'] = '2.0.3-rocketmq-SNAPSHOT'
  } else if (parentPom.endsWith("-rc")) {
//    ret['fs-stone-commons-client'] = '1.6.0-SNAPSHOT'
    ret['rocketmq-client'] = '5.3.0'
    ret['fs-rocketmq-support'] = '5.3.0-SNAPSHOT'
    ret['fs-common-mq'] = '5.3.0-SNAPSHOT'
    ret['fs-uc-api'] = '1.1.2-SNAPSHOT'
    ret['fs-sandbox-api'] = '2.0.4-SNAPSHOT'
    ret['fs-cache'] = '2.0.1-SNAPSHOT'
    ret['fs-change-set-api'] = '2.0.4-SNAPSHOT'
  }

  def regex = ~/^\d+(\d|\.)+\d+/
  ret.each { key, value ->
    def matcher = value =~ regex
    if (matcher.find()) {
      ret.put(key, matcher.group())
    } else {
      println "missMatch, key: ${key}, val: $value"
    }
  }
//  println("jar version config: ${ret}")
  return ret
}

static def extractVersion(String ver) {
  int pos = ver.indexOf('-')
  if (pos > 0) {
    ver = ver.substring(0, pos)
  }
  return ver
}

static def versionComparator(String a, String b) {
  def o1 = extractVersion(a).tokenize('.')
  def o2 = extractVersion(b).tokenize('.')
  def len = Math.min(o1.size(), o2.size())
  int ret = 0
  for (int i = 0; i < len; i++) {
    def v1 = o1[i] as int
    def v2 = o2[i] as int
    ret = v1 - v2
    if (ret != 0) {
      break
    }
  }
  return ret
}

static def isNullOrBlank(String s) {
  return s == null || s.trim() == ''
}

static def firstNotBlank(String... args) {
  for (String s : args) {
    if (!isNullOrBlank(s)) {
      return s
    }
  }
  return null
}

// 允许使用Java 17镜像的git模块
static def isAllowJava17(String mavenImage, String gitModule) {
  String[] allowJava17GitModules = [
    "fs-crm-web",
    "fs-document-convert-web-big",
    "fs-fsc-cgi",
    "fs-file-preview-service",
    "fs-document-convert-web",
    "fs-document-preview-cgi",
    "warehouse-batch-biz",
    "fs-sync-data-all",
    "fs-sync-data-task",
    "open-api-gateway-web",
    "open-api-admin-web"
    ]
  return !mavenImage.contains("openjdk17") || allowJava17GitModules.any { regex -> gitModule ==~ regex }
}

// 是否编译多架构镜像，默认启用，在忽略名单里的都不编译
static def multiArchBuildEnabled(String gitModule) {
  //哪些git模块不启用多架构，支持正则匹配
  String[] ignoreGitModules = ["empty"]
  return !ignoreGitModules.any { regex -> gitModule ==~ regex }
}

def scanSpringBootJarVersion(String glob) {
  String jars = sh(script: "jar -tf ${glob} |  grep BOOT-INF/lib/ | grep jar || true", returnStdout: true)
  def jarNames = jars.split('\n').collect { it.trim() }
  Map<String, List<String>> jarVersion = [:]
  def regex = ~/-(\d+).(\d+)/
  jarNames.each { jarName ->
    String name = jarName.replace("BOOT-INF/lib/", "")
    def matcher = name =~ regex
    if (matcher.find()) {
      def key = name.substring(0, matcher.start())
      def ver = name.substring(matcher.start(1))
      def versions = jarVersion.get(key)
      if (versions == null) {
          versions = []
          jarVersion.put(key, versions)
      }
      versions << ver
    } else {
      println "missMatch, name: ${name}"
    }
  } 
  return jarVersion

}
def scanJarVersion(String glob) {
  Map<String, List<String>> jarVersion = [:]
  println("find jar files from: ${glob}")
  def files = findFiles(glob: glob)
  // 寻找中横线后至少两位数字的版本号位置
  // commons-beanutils-1.9.4.jar
  // commons-lang3-3.12.0.jar
  // spring-plugin-metadata-1.2.0.RELEASE.jar
  // fs-metadata-api-8.6.0-SNAPSHOT.jar

  println "============  jar ================"
  def jarNames = files.collect { it.getName() }
  println(jarNames.sort().join('\n'))
  println "============  jar ================"

  def regex = ~/-(\d+).(\d+)/
  files.each { file ->
    String name = file.getName()
    def matcher = name =~ regex
    if (matcher.find()) {
      def key = name.substring(0, matcher.start())
      def ver = name.substring(matcher.start(1))
      def versions = jarVersion.get(key)
      if (versions == null) {
        versions = []
        jarVersion.put(key, versions)
      }
      versions << ver
    } else {
      println "missMatch, name: ${name}"
    }
  }
  return jarVersion
}

properties([
  [$class: 'BuildDiscarderProperty', strategy: [$class: 'LogRotator', numToKeepStr: '5000', artifactNumToKeepStr: '5000']],
  parameters([
    string(name: 'gitUrl', defaultValue: '', description: 'Git地址'),
    string(name: 'gitModule', defaultValue: '', description: 'Git子模块'),
    string(name: 'gitTag', defaultValue: '', description: 'Git分支或Tag'),
    string(name: 'commitId', defaultValue: '', description: 'Git提交ID'),
    string(name: 'artifactBaseImage', defaultValue: '', description: '基础镜像'),
    string(name: 'artifactImage', defaultValue: '', description: '目标镜像'),
    string(name: 'artifactRemark', defaultValue: '', description: '镜像备注'),
    choice(name: 'unitTest', choices: ['no', 'yes'], description: '是否执行单元测试?'),
    string(name: 'mavenOptions', defaultValue: '', description: 'maven 命令附加参数'),
    choice(name: 'forceBuild', choices: ['no', 'yes'], description: '是否强制构建？'),
    choice(name: 'dependencyCheck', choices: ['yes', 'no'], description: '是否进行依赖包检测？'),
    string(name: 'parentPom', defaultValue: '', description: '应用父POM'),
    choice(name: 'package', choices: ['war', 'jar'], description: '打包类型'),
    string(name: 'mavenImage', defaultValue: 'reg.firstshare.cn/base/fs-maven3.9:openjdk8', description: 'maven镜像'),
    string(name: 'author', defaultValue: 'unknown', description: '操作人'),
    string(name: 'uuid', defaultValue: '--', description: 'uuid')])
])

node('k8s-publish') {
  stage("param parse") {
    script {
      println "params: $params"
      if (isNullOrBlank(params.parentPom)) {
        error "parent pom is blank"
      }
    }
  }
  stage("build") {
    def proj = [
      "gitUrl"           : params.gitUrl.replace('http://', 'https://'),
      "module"           : params.gitModule,
      "tag"              : params.gitTag,
      "commitId"         : params.commitId,
      "artifactBaseImage": params.artifactBaseImage,
      "artifactImage"    : params.artifactImage,
    ]
    println "project: $proj"
    String projectID = proj.gitUrl.replace("https://git.firstshare.cn/", "").replace(".git", "")
    def workDir = projectID

    catchError(failFast: false) {
      pwd
      println "node: ${NODE_NAME}"
      println "projectId: ${projectID}"
      println "workDir: ${workDir}"
      println "${proj.gitUrl}, tag: ${proj.tag}"

      // 1. 检出代码
      stage('checkout and check image') {
        dir(workDir) {
          // 重置git避免更新失败
          if (fileExists('.git')) {
            try {
              sh(label: 'reset git', script: 'git reset --hard')
            } catch (e) {
              echoWithLabel("reset git error: " + e.message, proj.module)
              deleteDir()
            }
          }
          try {
            sh """git config --global http.sslVerify false"""
            checkout([$class                           : 'GitSCM',
                      branches                         : [[name: "${proj.commitId}"]],
                      doGenerateSubmoduleConfigurations: false,
                      extensions                       : [[$class: 'PruneStaleBranch'],
                                                          [$class: 'CheckoutOption', timeout: 30],
                                                          [$class: 'CloneOption', timeout: 30]],
                      submoduleCfg                     : [],
                      userRemoteConfigs                : [[credentialsId: '00000000-0000-0000-0000-111111111111', url: proj.gitUrl]]])
          } catch (e) {
            echoWithLabel("checkout error: " + e.message, projectID)
            deleteDir()
            error "git: ${proj.gitUrl} checkout失败，请稍后重试"
          }

          // 强制构建，删除本地的镜像
          if (params.forceBuild == 'yes') {
            proj["exist"] = false
          } else {
            // 尝试拉取docker镜像，如果失败则继续构建
            sh(label: 'image pull', script: "docker pull ${proj.artifactImage} || true")
            // 根据commitId确定是否已经存在了
            def cmd = "docker inspect --format='{{index .Config.Labels \"commitId\"}}' ${proj.artifact_image} || true"
            proj.exist = sh(label: 'image query', script: cmd, returnStdout: true).trim().contains(proj.commitId)
          }
        }
      }
      // 2. 修改父pom
      if (proj.exist) {
        echoWithLabel("image exist, skip build", proj.module)
      } else {
        stage('modify pom') {
          dir(workDir) {
            String[] poms = sh(script: "find . -name pom.xml", returnStdout: true).trim().split('\n')
            // 检查maven-war-plugin的版本
            def hasError = false
            poms.each { pom ->
              String outLine = sh(label: "检测maven-war-plugin版本", script: "grep '<artifactId>maven-war-plugin</artifactId>' ${pom} -A 2 || true", returnStdout: true)
              if (outLine?.contains("<version>2")) {
                def errMsg = "插件 maven-war-plugin 的版本必须大于 3.0.0 版本(建议从公司父pom继承版本号，不要自己指定)。" +
                  "\npom文件: ${pom}" +
                  "\n插件当前版本: ${outLine.trim()}" +
                  "\n插件bug描述: https://issues.apache.org/jira/browse/MWAR-296"
                echoWithLabel(errMsg, pom)
                hasError = true
              }
            }
            if (hasError) {
              error "请使用公司级父pom文件中使用的的maven插件版本号，不需要自己指定"
            }
            // 修改父pom，这个是业务选择的值
            def targetParentPom = params.parentPom
            // 修改 Spring Cloud 父pom，增加版本号后缀
            def springCloudParentPom = "fxiaoke-spring-cloud-parent"
            for (item in ["-forceecrm-rc", "-alpha", "-rc", "-release", "-rc-rocketmq-5.3.0"]) {
              if (params.parentPom.endsWith(item)) {
                springCloudParentPom += item
                break;
              }
            }

            poms.each { pom ->
              sh(label: "修改父pom: ${pom}", script: "sed -i 's@<artifactId>fxiaoke-parent-pom</artifactId>@<artifactId>${targetParentPom}</artifactId>@g' ${pom}", returnStatus: true)
              sh(label: "修改Spring Cloud父pom: ${pom}", script: "sed -i 's@<artifactId>fxiaoke-spring-cloud-parent</artifactId>@<artifactId>${springCloudParentPom}</artifactId>@g' ${pom}", returnStatus: true)
              sh(label: "查看父pom: ${pom}", script: "grep fxiaoke-parent-pom -C 3 ${pom} || true")
              sh(label: "查看Spring Cloud父pom: ${pom}", script: "grep fxiaoke-spring-cloud-parent -C 3 ${pom} || true")
            }
          }
        }
        // 3. 编译
        stage('maven build') {
          dir(workDir) {
            def mvnCmd = 'mvn -B -fae -ntp -am -e -U clean package -Dmaven.javadoc.skip -Dsun.jnu.encoding=UTF-8 -Dfile.encoding=UTF-8 '
            if (proj.module) {
              mvnCmd += ' -pl ' + proj.module
            }
            if (params.unitTest == 'no') {
              mvnCmd += ' -Dmaven.test.skip'
            }
            if (params.mavenOptions) {
              mvnCmd += ' ' + params.mavenOptions
            }
            mvnCmd += ' -Dmaven.wagon.http.ssl.insecure=true -Dserver -Dhttps.protocols=TLSv1.2 -Djava.awt.headless=true -Dorg.slf4j.simpleLogger.showDateTime=true -Dorg.slf4j.simpleLogger.dateTimeFormat=HH:mm:ss.SSS'
            String hostHostname = sh(script: "hostname", returnStdout: true).trim()
            String mavenRepoHost = hostHostname.contains("firstshare") ? "maven.firstshare.cn" : "maven.foneshare.cn"
            docker.image("${params.mavenImage}").inside('--privileged -v $HOME/.m2:/root/.m2 --network=host -e LANG=en_US.UTF-8 -e LC_ALL=en_US.UTF-8 -e MAVEN_OPTS=-Xmx4096m ' + "-e MAVEN_REPO_HOST=${mavenRepoHost} -h maven.${hostHostname}") {
              sh(label: 'mvn building', script: mvnCmd)
            }
          }
        }

        // 3.2 java check，辅助升级Java21版本
        stage('java21 check') {
          dir(workDir) {
            String hostHostname = sh(script: "hostname", returnStdout: true).trim()
            // only for firstshare  
            if (!hostHostname.contains("firstshare")) {
              echoWithLabel("skip java check for foneshare", 'java21 check')
              return
            }
            // 如果使用Java17镜像，并且不是白名单里的模块，则报错 
            if (!isAllowJava17(params.mavenImage, proj.module)) {
              error "我们准备废弃Java17，推荐Java21，如果你的应用只能支持Java17，请联系刘全胜、吴志辉加白名单忽此校验"
            }
            // params.mavenImage是Java21以上
            if (!params.mavenImage.contains("openjdk2")) {
              echoWithLabel("skip java check, only support java21+", 'java21 check')
              return
            }
            String mavenRepoHost = hostHostname.contains("firstshare") ? "maven.firstshare.cn" : "maven.foneshare.cn"
            def mvnCmd = 'mvn -B -fae -ntp -am -e -U process-classes org.eclipse.emt4j:emt4j-maven-plugin:0.91:process -DfromVersion=8 -DtoVersion=21 -DoutputFile=target/emt4j-report.html '
            if (proj.module) {
              mvnCmd += ' -pl ' + proj.module
            }
            if (params.mavenOptions) {
              mvnCmd += ' ' + params.mavenOptions
            }
            mvnCmd += ' -Dmaven.wagon.http.ssl.insecure=true -Dserver -Dhttps.protocols=TLSv1.2 -Djava.awt.headless=true -Dorg.slf4j.simpleLogger.showDateTime=true -Dorg.slf4j.simpleLogger.dateTimeFormat=HH:mm:ss.SSS'

            catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE', message: 'Java 21 API兼容性报告运行错误') {
              docker.image("${params.mavenImage}").inside('--privileged -v $HOME/.m2:/root/.m2 --network=host -e MAVEN_OPTS=-Xmx2048m ' + "-e MAVEN_REPO_HOST=${mavenRepoHost} -h maven.${hostHostname}") {
                sh(label: 'mvn building', script: mvnCmd)
              }
              // 发布成HTML报告
              publishHTML (target : [allowMissing: true,
                alwaysLinkToLastBuild: false,
                keepAll: true,
                reportDir: 'target',
                reportFiles: 'emt4j-report.html',
                reportName: 'java-emt4j-report',
                reportTitles: proj.module + ' Java 21 API兼容性报告'])
              
              echo "请点击URL查看${proj.module}的Java 21 API兼容性报告: ${env.BUILD_URL}java-emt4j-report"

              sh(label: 'delete emt4j cache', script: 'rm -rf .emt4j')

            }

          }
        }

        // 4. 依赖包版本校验
        if (params.dependencyCheck == 'yes') {
          stage('dependencies check') {
            dir(workDir) {
              def basePath = proj.module ? proj.module + "/" : ''
              def jars = scanJarVersion("${basePath}target/*/WEB-INF/lib/*.jar")
              def size = jars.size()
              // spring boot 项目单独扫描
              if (size == 0) {
                jars = scanSpringBootJarVersion("${basePath}target/*.jar")
                size = jars.size()
              }
              echoWithLabel("found ${size} jars", 'scan jars')

              // 检查jar包的最低版本号要求
              def minVersions = jarVersionNecessaryConf(params.parentPom)
              List<String> conflicts = []
              minVersions.each { k, v ->
                def kinds = jars[k]
                def min = minVersions[k]
                if (min && kinds) {
                  kinds.each { now ->
                    if (versionComparator(min, now) > 0) {
                      echoWithLabel("jar包版本过低: ${k}-${now} < ${min}，(建议从公司父pom继承版本号，不要自己指定)", '最低版本限制')
                      conflicts.add("${k}-${now}")
                    }
                  }
                }
              }
              if (conflicts) {
                error "jar包版本过低: ${conflicts}"
              }

              // 检查特定druid版本的一致性
              def druidVersion = jars['druid']?.first()
              def mybatisVersion = jars['mybatis-spring-support']?.first()
              def sql2esVersion = jars['fs-sql2esdsl']?.first()
              println "druidVersion: ${druidVersion}, mybatisVersion: ${mybatisVersion}, sql2esVersion: ${sql2esVersion}"

              catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE', message: '存在同名jar包版本冲突') {
                // 检查所有同名的jar包
                conflicts = []
                // 有些jar包名一样，但是内部的类包名不一样，实际运行不冲突，这里先忽略
                Set<String> ignores = ['cglib', 'annotations', 'metrics-core', 'rocketmq-client', 'rocketmq-common', 'rocketmq-remoting', 'protostuff-api']
                jars.each { k, v ->
                  if (ignores.contains(k)) {
                    return
                  }
                  if (k.startsWith('netty') && k.contains('native')) {
                    return
                  }
                  if (v.size() > 1) {
                    conflicts.add(k)
                  }
                }
                if (conflicts) {
                  conflicts.each {
                    def ver = jars[it]
                    echoWithLabel("存在同名包版本冲突: ${it}: ${ver}", 'jar conflicts')
                  }
                  error "存在同名包版本冲突: ${conflicts}"
                }
              }

              catchError(buildResult: 'SUCCESS', stageResult: 'UNSTABLE', message: 'cglib-nodep不可与cglib、asm共存，这里有坑，请留意') {
                def cglibNodep = jars['cglib-nodep']?.first()
                if (cglibNodep) {
                  def cglib = jars['cglib']?.first()
                  def asm = jars['asm']?.first()
                  if (cglib || asm) {
                    error "cglib-nodep不可与cglib、asm共存，若要使用cglib-nodep，则需要排除掉所有的cglib和asm，这是因为cglib-nodep = cglib + asm。" +
                      "参考资料：https://sunyinjie.github.io/2018/04/01/Cglib%E5%BC%95%E5%8F%91%E7%9A%84%E8%A1%80%E6%A1%88%E2%80%94%E2%80%94Jar%E5%8C%85%E4%BE%9D%E8%B5%96%E5%86%B2%E7%AA%81%E6%80%BB%E7%BB%93/"
                  }
                }
              }

              def rocketmqClientVer = jars['rocketmq-client']?.first()
              def rocketmqCommonVer = jars['rocketmq-common']?.first()
              def rocketmqAclVer = jars['rocketmq-acl']?.first()
              def rocketmqRemotingVer = jars['rocketmq-remoting']?.first()
              if (rocketmqClientVer?.startsWith("5.3.0")) {
                println "rocketmq-client: ${rocketmqClientVer}, rocketmq-common: ${rocketmqCommonVer}, rocketmq-acl: ${rocketmqAclVer}, rocketmq-remoting: ${rocketmqRemotingVer}"
                if (rocketmqCommonVer && rocketmqCommonVer != rocketmqClientVer) {
                  error "rocketmq-common 版本号 必须要跟 rocketmq-client 版本一致。详情请联系 @谷广田"
                }
                if (rocketmqAclVer && rocketmqAclVer != rocketmqClientVer) {
                  error "rocketmq-acl 版本号 必须要跟 rocketmq-client 版本一致。详情请联系 @谷广田"
                }
                if (rocketmqRemotingVer && rocketmqRemotingVer != rocketmqClientVer) {
                  error "rocketmq-remoting 版本号 必须要跟 rocketmq-client 版本一致。详情请联系 @谷广田"
                }
              }

              def fs_metadata_provider_ver = jars['fs-metadata-provider']?.first()
              if (fs_metadata_provider_ver?.startsWith("9.4.0-SNAPSHOT")) {
                def fs_stone_commons_client_ver = jars['fs-stone-commons-client']?.first()
                def stoneCommonClientV = 0
                if(fs_stone_commons_client_ver) {
                  stoneCommonClientV = fs_stone_commons_client_ver.replaceAll(/jar|SNAPSHOT|[-.]/, "").toInteger()
                }
                if (stoneCommonClientV < 165) {
                  error "当 fs-metadata-provider 的版本号为 9.4.0-SNAPSHOT 时，fs-stone-commons-client 的版本需大于等于 1.6.5。详情咨询 @钱凌锋 @安宜龙 \n" +
                    "fs-metadata-provider 当前版本：${fs_metadata_provider_ver}, fs-stone-commons-client 当前版本: ${fs_stone_commons_client_ver}"
                }
              }
            }
          }
        }
        // 5. 构建docker镜像
        stage('image build') {
          dir(workDir) {
            def basePath = proj.module ?: '.'
            def pkg = ""
            String pkgPath = sh(label: "尝试获取War部署包文件", script: "ls ${basePath}/target/*.war 2>/dev/null | head -n 1", returnStdout: true).trim()
            if (pkgPath.endsWith(".war")) {
              pkg = "war"
            } else {
              //注意要过滤掉源码jar包
              pkgPath = sh(label: "尝试获取Jar部署包文件", script: "ls ${basePath}/target/*.jar 2>/dev/null | grep -v 'sources.jar' | head -n 1", returnStdout: true).trim()
              if (pkgPath.endsWith(".jar")) {
                pkg = "jar"
              } else {
                error "未找到部署的包类型，目前只支持jar和war"
              }
            }
            def baseImage = proj.artifactBaseImage
            def gitUrl = proj.gitUrl
            def module = proj.module
            def tag = proj.tag
            def commitId = proj.commitId
            def author = params.author.replace(" ", "") ?: "unknown"
            def buildRemark = params.artifactRemark ?: ''
            buildRemark = buildRemark.replace(' ', '')
            // 由于通过Harbor API无法获取容器Label,同时维护者信息业务上基本不用，因此把备注信息写入MAINTAINER中
            def content = "FROM ${baseImage}\n" +
              "LABEL MAINTAINER=${author}\n" +
              "LABEL gitUrl=${gitUrl}\n" +
              "LABEL gitModule=${module}\n" +
              "LABEL gitTag=${tag}\n" +
              "LABEL commitId=${commitId}\n" +
              "LABEL buildRemark=${buildRemark}\n" +
              "ENV ARTIFACT_TYPE=${pkg}\n" +
              "ADD ${pkgPath} /fs-artifact/fs.${pkg}\n"

            // 写入本地的Dockerfile
            sh(label: 'delete old Dockerfile', script: 'rm -f Dockerfile')
            echoWithLabel(content, "Dockerfile")
            writeFile file: 'Dockerfile', text: content, encoding: 'UTF-8'
            //线上环境还没有启用buildx，等线上也启动并稳定后，留下面两行即可
            String hostHostname = sh(script: "hostname", returnStdout: true).trim()
            def multiArchEnabled = hostHostname.contains("firstshare")
            if (multiArchEnabled && multiArchBuildEnabled(module)) {
              def platform = multiArchEnabled ? "linux/amd64,linux/arm64" : "linux/amd64"
              sh(label: 'docker build push', script: "docker buildx build --push --platform ${platform} --provenance=false --output=oci-mediatypes=false --tag ${proj.artifactImage} .")
            } else {
              sh(label: 'docker build', script: "docker build --quiet -t ${proj.artifactImage} .")
              sh(label: 'docker push', script: "docker push ${proj.artifactImage}")
            }
          }
        }
      }
    }
  }
}
